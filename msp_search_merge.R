# 需要的包
library(readr)
library(openxlsx)
library(stringr)

# 用户输入路径设置
input_path <- "C:/Users/<USER>/Desktop/test/input"

output_base_path <- "C:/Users/<USER>/Desktop/test/output"

# 检查路径是否有效
if (input_path == "") {
  cat("未输入输入路径，使用当前文件夹\n")
  input_path <- "."
}

if (output_base_path == "") {
  cat("未输入输出路径，使用默认路径：./output\n")
  output_base_path <- "./output"
}

# 1. 获取指定文件夹中的所有.msp文件
msp_files <- list.files(path = input_path, pattern = "\\.msp$", full.names = TRUE)

if (length(msp_files) == 0) {
  cat(sprintf("在路径 %s 中没有找到.msp文件！\n", input_path))
  quit()
}

cat(sprintf("在 %s 中找到 %d 个.msp文件，开始批量处理...\n", input_path, length(msp_files)))
cat(sprintf("输出路径: %s\n", output_base_path))

# 批量处理每个.msp文件
for (file_idx in seq_along(msp_files)) {
  msp_file <- msp_files[file_idx]
  cat(sprintf("正在处理: %s\n", basename(msp_file)))
  
  # 读取msp文件
  lines <- readLines(msp_file, encoding = "UTF-8")
  
  # 2. 解析内容
  get_blocks <- function(lines) {
    blocks <- list()
    block <- c()
    for (line in lines) {
      if (str_trim(line) == "" && length(block) > 0) {
        blocks[[length(blocks) + 1]] <- block
        block <- c()
      } else {
        block <- c(block, line)
      }
    }
    if (length(block) > 0) blocks[[length(blocks) + 1]] <- block
    return(blocks)
  }
  
  blocks <- get_blocks(lines)
  
  # 新建输出文件夹
  output_dir <- file.path(output_base_path, tools::file_path_sans_ext(basename(msp_file)))
  if (!dir.exists(output_dir)) dir.create(output_dir, recursive = TRUE)
  
  # 创建POS和NEG子文件夹
  pos_dir <- file.path(output_dir, "POS")
  neg_dir <- file.path(output_dir, "NEG")
  if (!dir.exists(pos_dir)) dir.create(pos_dir)
  if (!dir.exists(neg_dir)) dir.create(neg_dir)
  
  # 3. 处理每个block
  for (i in seq_along(blocks)) {
    block <- blocks[[i]]
    # 找到NAME和Num Peaks的位置
    name_idx <- grep("^NAME", block, ignore.case = TRUE)
    num_peaks_idx <- grep("^Num Peaks", block, ignore.case = TRUE)
    if (length(name_idx) == 0 || length(num_peaks_idx) == 0) next
    
    # 提取元信息和峰信息
    meta_info <- block[name_idx:(num_peaks_idx-1)]
    peaks_info <- block[num_peaks_idx:length(block)]
    # 峰信息到下两个空行之间
    peaks <- c()
    empty_count <- 0
    for (j in seq_along(peaks_info)) {
      if (str_trim(peaks_info[j]) == "") {
        empty_count <- empty_count + 1
        if (empty_count == 2) break
      } else {
        peaks <- c(peaks, peaks_info[j])
        empty_count <- 0
      }
    }
    
    # 4. 生成文件名
    msp_filename <- basename(msp_file)
    msp_prefix <- str_extract(msp_filename, "^[0-9]+")
    if (is.na(msp_prefix)) {
      msp_prefix <- tools::file_path_sans_ext(msp_filename)
    }
    name_num <- i
    # 提取PRECURSORMZ:后面的数字
    precursor_mz_line <- grep("^PRECURSORMZ:", block, ignore.case = TRUE)
    if (length(precursor_mz_line) > 0) {
      precursor_mz_value <- str_extract(block[precursor_mz_line[1]], "[0-9]+\\.[0-9]+")
      if (is.na(precursor_mz_value)) {
        precursor_mz_value <- "0"
      }
    } else {
      precursor_mz_value <- "0"
    }
    
    # 提取IONMODE:后面的值
    ionmode_line <- grep("^IONMODE:", block, ignore.case = TRUE)
    if (length(ionmode_line) > 0) {
      ionmode_value <- str_trim(sub("^IONMODE:", "", block[ionmode_line[1]]))
      if (tolower(ionmode_value) == "positive") {
        target_dir <- pos_dir
      } else if (tolower(ionmode_value) == "negative") {
        target_dir <- neg_dir
      } else {
        target_dir <- output_dir  # 默认放在主文件夹
      }
    } else {
      target_dir <- output_dir  # 默认放在主文件夹
    }
    
    out_file <- file.path(target_dir, sprintf("%s_%d_%s.xlsx", msp_prefix, name_num, precursor_mz_value))
    
    # 5. 写入Excel
    wb <- createWorkbook()
    addWorksheet(wb, "sheet1")
    addWorksheet(wb, "sheet2")
    writeData(wb, "sheet1", data.frame(peaks), colNames = FALSE)
    writeData(wb, "sheet2", data.frame(meta_info), colNames = FALSE)
    saveWorkbook(wb, out_file, overwrite = TRUE)
  }
  
  cat(sprintf("完成处理: %s\n", basename(msp_file)))
}

cat("所有.msp文件处理完成！\n")