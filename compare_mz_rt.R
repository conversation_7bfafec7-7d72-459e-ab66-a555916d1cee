# 比较CT和NCT文件中的mz和rt数据
# 找出共有部分和各自独有的部分

# 加载必要的库
library(dplyr)
library(readr)

# 读取数据文件
ct_data <- read_csv("CT/21_pklt1_neg.csv")
nct_data <- read_csv("NCT/21_pklt1_neg.csv")

# 查看数据结构
cat("CT数据维度:", dim(ct_data), "\n")
cat("NCT数据维度:", dim(nct_data), "\n")
cat("CT数据列名:", colnames(ct_data), "\n")
cat("NCT数据列名:", colnames(nct_data), "\n")

# 设置匹配阈值
mz_threshold <- 0.005  # mz差值阈值
rt_threshold <- 3      # rt差值阈值

# 函数：找到匹配的行
find_matches <- function(data1, data2, mz_thresh, rt_thresh) {
  matches <- data.frame()
  data1_matched_indices <- c()
  data2_matched_indices <- c()
  
  for (i in 1:nrow(data1)) {
    mz1 <- data1$mz[i]
    rt1 <- data1$rt[i]
    
    # 在data2中找到mz和rt都在阈值范围内的行
    mz_diff <- abs(data2$mz - mz1)
    rt_diff <- abs(data2$rt - rt1)
    
    # 找到同时满足mz和rt阈值条件的行
    match_indices <- which(mz_diff <= mz_thresh & rt_diff <= rt_thresh)
    
    if (length(match_indices) > 0) {
      # 如果有多个匹配，选择距离最近的那个
      if (length(match_indices) > 1) {
        distances <- sqrt(mz_diff[match_indices]^2 + rt_diff[match_indices]^2)
        best_match <- match_indices[which.min(distances)]
      } else {
        best_match <- match_indices[1]
      }
      
      # 检查这个匹配是否已经被使用过
      if (!best_match %in% data2_matched_indices) {
        # 记录匹配
        data1_matched_indices <- c(data1_matched_indices, i)
        data2_matched_indices <- c(data2_matched_indices, best_match)
        
        # 创建匹配记录
        match_record <- data.frame(
          CT_index = i,
          NCT_index = best_match,
          CT_mz = mz1,
          NCT_mz = data2$mz[best_match],
          mz_diff = abs(mz1 - data2$mz[best_match]),
          CT_rt = rt1,
          NCT_rt = data2$rt[best_match],
          rt_diff = abs(rt1 - data2$rt[best_match])
        )
        
        matches <- rbind(matches, match_record)
      }
    }
  }
  
  return(list(
    matches = matches,
    data1_matched = data1_matched_indices,
    data2_matched = data2_matched_indices
  ))
}

# 执行匹配
cat("正在寻找匹配的数据点...\n")
match_result <- find_matches(ct_data, nct_data, mz_threshold, rt_threshold)

# 提取结果
matches <- match_result$matches
ct_matched_indices <- match_result$data1_matched
nct_matched_indices <- match_result$data2_matched

# 创建共有部分的表格
if (nrow(matches) > 0) {
  # 合并CT和NCT的匹配数据
  common_data <- data.frame(
    match_id = 1:nrow(matches),
    CT_mz = matches$CT_mz,
    CT_rt = matches$CT_rt,
    NCT_mz = matches$NCT_mz,
    NCT_rt = matches$NCT_rt,
    mz_difference = matches$mz_diff,
    rt_difference = matches$rt_diff
  )
  
  # 添加CT的其他列数据
  ct_matched_data <- ct_data[ct_matched_indices, ]
  nct_matched_data <- nct_data[nct_matched_indices, ]
  
  # 重命名列以区分CT和NCT
  ct_cols <- colnames(ct_matched_data)
  nct_cols <- colnames(nct_matched_data)
  
  # 除了mz和rt，其他列加上前缀
  for (col in ct_cols) {
    if (!col %in% c("mz", "rt")) {
      colnames(ct_matched_data)[colnames(ct_matched_data) == col] <- paste0("CT_", col)
    }
  }
  
  for (col in nct_cols) {
    if (!col %in% c("mz", "rt")) {
      colnames(nct_matched_data)[colnames(nct_matched_data) == col] <- paste0("NCT_", col)
    }
  }
  
  # 移除重复的mz和rt列
  ct_matched_data <- ct_matched_data[, !colnames(ct_matched_data) %in% c("mz", "rt")]
  nct_matched_data <- nct_matched_data[, !colnames(nct_matched_data) %in% c("mz", "rt")]
  
  # 合并所有数据
  common_full_data <- cbind(common_data, ct_matched_data, nct_matched_data)
  
  cat("找到", nrow(matches), "个匹配的数据点\n")
} else {
  common_data <- data.frame()
  common_full_data <- data.frame()
  cat("没有找到匹配的数据点\n")
}

# 找出CT独有的数据
ct_unique_indices <- setdiff(1:nrow(ct_data), ct_matched_indices)
ct_unique_data <- ct_data[ct_unique_indices, ]
cat("CT独有的数据点:", length(ct_unique_indices), "个\n")

# 找出NCT独有的数据
nct_unique_indices <- setdiff(1:nrow(nct_data), nct_matched_indices)
nct_unique_data <- nct_data[nct_unique_indices, ]
cat("NCT独有的数据点:", length(nct_unique_indices), "个\n")

# 保存结果到CSV文件
if (nrow(common_data) > 0) {
  write_csv(common_data, "common_mz_rt_summary.csv")
  write_csv(common_full_data, "common_mz_rt_full.csv")
  cat("共有部分已保存到: common_mz_rt_summary.csv 和 common_mz_rt_full.csv\n")
}

write_csv(ct_unique_data, "CT_unique_mz_rt.csv")
write_csv(nct_unique_data, "NCT_unique_mz_rt.csv")

cat("CT独有部分已保存到: CT_unique_mz_rt.csv\n")
cat("NCT独有部分已保存到: NCT_unique_mz_rt.csv\n")

# 显示统计信息
cat("\n=== 统计摘要 ===\n")
cat("CT总数据点:", nrow(ct_data), "\n")
cat("NCT总数据点:", nrow(nct_data), "\n")
cat("共有数据点:", nrow(matches), "\n")
cat("CT独有数据点:", length(ct_unique_indices), "\n")
cat("NCT独有数据点:", length(nct_unique_indices), "\n")

if (nrow(matches) > 0) {
  cat("\n=== 匹配质量统计 ===\n")
  cat("mz差值范围:", round(min(matches$mz_diff), 6), "-", round(max(matches$mz_diff), 6), "\n")
  cat("mz差值平均:", round(mean(matches$mz_diff), 6), "\n")
  cat("rt差值范围:", round(min(matches$rt_diff), 3), "-", round(max(matches$rt_diff), 3), "\n")
  cat("rt差值平均:", round(mean(matches$rt_diff), 3), "\n")
  
  # 显示前几个匹配的例子
  cat("\n=== 前5个匹配示例 ===\n")
  print(head(common_data, 5))
}

cat("\n分析完成！\n")
