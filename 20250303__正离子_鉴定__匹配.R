#整合数据库20240911，根据采集的母离子与数据库中的母离子匹配，从而获取匹配的行对，用于后续提取出具体的数据信息。
#1提取所有已采集的母离子及二级信息
#1111.采用metid读取差异变量的一级和二级数据，(然后与spl列表进行比对后重新生成spl列表)
library(metid)
library(tidyverse)
library(gtools)
library(dplyr)
library(openxlsx)
dir.create("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0405/pos_shui_spl/01_excel_he/")
setwd("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0405/pos_shui_spl/01_excel_he/")
getwd()
save_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0405/pos_shui_spl/01_excel_he/"
path <- file.path("E:/R_data/20250403_neg_spl/1111")
file.pos <- mixedsort(dir(file.path(path), pattern = "\\.mzXML$", full.names = TRUE))
###1.不合并同一张谱中来源于同一个母离子的二级碎片
matrix_ms1_all_m <- NULL
for(m in 1:length(file.pos)){
  file.pos_m <- file.pos[m]
  tryCatch({
    ms2.data.pos <- read_mzxml(file = file.pos_m, threads = threads)
    ms1.info.pos <- lapply(ms2.data.pos, function(x) {
      x[[1]]
    })
    ms1.info.pos <- do.call(rbind, ms1.info.pos) %>% as.data.frame()
    ms2.info.pos <- lapply(ms2.data.pos, function(x) {
      x[[2]]
    })  
    mzdec <- 4; rtdec <- 3;
    rtfmt <- paste("%.", rtdec, "f", sep = "")
    mzfmt <- paste("%.", mzdec, "f", sep = "")
    var1<- paste("nM", sprintf(mzfmt, ms1.info.pos[,"mz"]), "T", sprintf(rtfmt, ms1.info.pos[,"rt"]), sep = "")
    ms1.info.pos$name <- var1
    ms2.info.pos <- ms2.info.pos[sapply(ms2.info.pos, nrow) > 0]
    if(length(ms2.info.pos) > 0){
      wb <- createWorkbook()
      # 循环遍历列表中的矩阵，并将它们添加到工作簿中
      for (i in seq_along(ms2.info.pos)) {
        # 添加一个新的工作表，名称为矩阵的名称
        if (nrow(ms2.info.pos[[i]]) > 0){
          addWorksheet(wb, sheetName = ms1.info.pos[i,1])
          # 将矩阵写入当前工作表
          writeData(wb, sheet = ms1.info.pos[i,1], x = ms2.info.pos[[i]])
        } 
      }
      # 保存工作簿到文件
      #saveWorkbook(wb, paste(file.pos_m,".xlsx",sep = ""), save_path, overwrite = TRUE)
      saveWorkbook(wb, file = file.path(save_path, paste0(basename(file.pos_m), ".xlsx")), overwrite = TRUE)
      matrix_ms1_all_m <- rbind(matrix_ms1_all_m,ms1.info.pos) 
    }
  }, error = function(e) {
    print(paste("跳过文件:", file.pos_m))
  })
}
#write.csv(matrix_ms1_all_m,"F:/MS_20240131/rawdata/ms2_pos_mzxml/excel/spl_experiment_accquired.csv")


###2222.合并同一张谱中来源于同一个母离子的二级碎片
matrix_ms1_all_m <- NULL
#temp.ms2.data <- read_mgf(file = file.pos)
for(m in 1:length(file.pos)){
  file.pos_m <- file.pos[m]
  ms2.data.pos <- read_mzxml(file = file.pos_m, threads = threads)
  ms1.info.pos <- lapply(ms2.data.pos, function(x) {
    x[[1]]
  })
  ms1.info.pos <- do.call(rbind, ms1.info.pos) %>% as.data.frame()
  ms2.info.pos <- lapply(ms2.data.pos, function(x) {
    x[[2]]
  })   
  order_data <- ms1.info.pos
  order_data$Designate_PC <- rep(NA,dim(order_data)[1])
  #First PC 
  order_data[1,dim(ms1.info.pos)[2]+1] = paste("PC",1,sep = "")    
  PC_record <- as.data.frame(order_data[1,c("mz","rt","Designate_PC")])    
  rtTol <- 5
  mzTol <- 0.005
  
  if (length(order_data$Designate_PC)>0){
    #Designate PC based on the intensity of the peak.
    if(length(order_data$Designate_PC) > 1){
      for (i in 2:length(order_data$Designate_PC)){
         # i=1
        #Record the location of the target PC
        loc <- which(order_data[i,"rt"] >= PC_record$rt - rtTol & order_data[i,"rt"] <= PC_record$rt + rtTol & order_data[i,"mz"] >= PC_record$mz - mzTol & order_data[i,"mz"] <= PC_record$mz + mzTol) 	  	
        # If the chosen peak is located inside the tolerance of any existing PC
        if(length(loc) >0 ){  
          if(length(loc) >1 ){
            #ubd[[i]] <- i	  
            ##The ambiguous peaks in different PCs will be designated into the closest PC.		
            mz_rt_diff <- list()      
            #添加化学位移和sigma及gamma的误差  
            for (j in 1:length(loc)){
              mz_rt_diff[[j]] <- sqrt((order_data[i,"rt"] - PC_record$rt[loc[j]])^2+(order_data[i,"mz"] - PC_record$mz[loc[j]])^2)
            }  
            mz_rt_diff_min <- which.min(mz_rt_diff) 
            order_data$Designate_PC[i] <- PC_record$Designate_PC[loc][mz_rt_diff_min] 
            #updating the chemical shift of the PC  
            Update_PC <- PC_record$Designate_PC[loc][mz_rt_diff_min]  
            PC_loc <- which(Update_PC==order_data$Designate_PC)  
            H_rt <- order_data[PC_loc,"rt"]
            H_mz <- order_data[PC_loc,"mz"]
            PC_record$rt[loc][mz_rt_diff_min] <- mean(H_rt)
            #PC_record$rt[loc][mz_rt_diff_min] <- H_rt[length(PC_loc)] 
            PC_record$mz[loc][mz_rt_diff_min] <- mean(H_mz)	
            #PC_record$mz[loc][mz_rt_diff_min] <- H_mz[length(PC_loc)]
          }else{  
            #peak is uniquely designated to a specific PC
            order_data$Designate_PC[i] <- PC_record$Designate_PC[loc]
            #updating the chemical shift of the PC
            Update_PC <- PC_record$Designate_PC[loc]
            PC_loc <- which(order_data$Designate_PC == Update_PC)
            H_rt <- order_data[PC_loc,"rt"]
            H_mz <- order_data[PC_loc,"mz"]
            PC_record$rt[loc] <- mean(H_rt)
            PC_record$mz[loc] <- mean(H_mz)
            #PC_record$rt[loc] <- H_rt[length(PC_loc)] list_ord_all
            #PC_record$mz[loc] <- H_mz[length(PC_loc)] 			
          }   
          # If the chosen peak is located outside the tolerance of any existing PC     
        }else{
          n <- length(PC_record$Designate_PC)+1   
          order_data$Designate_PC[i] = paste("PC",n,sep = "")   
          PC_record <- rbind(PC_record,order_data[i,c("mz","rt","Designate_PC")]) 
        }    
      }		
      matrix_ms2_all <- NULL
      matrix_ms1_all <- NULL
      
      
      for(nm in 1:dim(PC_record)[1]){
        ind_n <- which(order_data$Designate_PC %in% PC_record$Designate_PC[nm])
        list_ord <- ms2.info.pos[ind_n]
        list_ord <- list_ord[which(sapply(list_ord, nrow) != 0)]
        
        #######################################################################
        # 修改的关键部分：处理list_ord为空的情况
        if (length(list_ord) == 0) {
          # 当没有MS2数据时，保存母离子信息并创建空的MS2数据框
          matrix_ms1 <- PC_record[nm, ]
          matrix_ms2_all[[nm]] <- data.frame(mz = numeric(), 
                                             intensity = numeric(),
                                             stringsAsFactors = FALSE)
          matrix_ms1_all <- rbind(matrix_ms1_all, matrix_ms1)
          
          # 生成文件名（保持与有数据时相同的格式）
          mzdec <- 4; rtdec <- 3
          rtfmt <- paste("%.", rtdec, "f", sep = "")
          mzfmt <- paste("%.", mzdec, "f", sep = "")
          var1 <- paste("nM", sprintf(mzfmt, matrix_ms1[,"mz"]), 
                        "T", sprintf(rtfmt, matrix_ms1[,"rt"]), sep = "")
          
          # 创建工作簿并保存（即使没有数据也保持文件结构一致）
          if(!exists("wb")) wb <- createWorkbook()
          addWorksheet(wb, sheetName = var1)
          writeData(wb, sheet = var1, x = matrix_ms2_all[[nm]])
          
          next  # 现在这个next是在for循环内，可以正常工作
        }
        
        # 原有处理逻辑保持不变
        list_ord_all <- list()
        for (df in 1:length(list_ord)) {
          if (nrow(list_ord[[df]]) > 0) {
            # 确保列名一致
            colnames(list_ord[[df]]) <- c("mz", "intensity")
            list_ord_all[[df]] <- list_ord[[df]]
          }
        }
        #合并来自同一母离子的二级碎片
        if(length(list_ord_all) > 0){
          mat_ord <- do.call(rbind,list_ord_all)
          #同时写出该二级碎片的母离子信息
          matrix_ms1 <- PC_record[nm,]
          #同一个碎片离子标记为相同的编号，为按强度加和做准备
          order_data_sum <- mat_ord
          #First PC   
          order_data_sum$Designate_PC <- rep(NA,dim(order_data_sum)[1])
          order_data_sum[1,dim(mat_ord)[2]+1] = paste("PC",1,sep = "")    
          PC_record_sum <- as.data.frame(order_data_sum[1,c("mz","intensity","Designate_PC")])    
          mzTol <- 0.005
          if(length(order_data_sum$Designate_PC) > 1){
            #ubd<-NULL    
            #Designate PC based on the intensity of the peak.
            for (ii in 2:length(order_data_sum$Designate_PC)){
              #Record the location of the target PC
              loc <- which(order_data_sum[ii,"mz"] >= PC_record_sum$mz - mzTol & order_data_sum[ii,"mz"] <= PC_record_sum$mz + mzTol) 	  	
              # If the chosen peak is located inside the tolerance of any existing PC
              if(length(loc) >0 ){  
                if(length(loc) >1 ){
                  #ubd[[i]] <- i	  
                  ##The ambiguous peaks in different PCs will be designated into the closest PC.		
                  mz_rt_diff <- list()      
                  #添加化学位移和sigma及gamma的误差  
                  for (jj in 1:length(loc)){
                    mz_rt_diff[[jj]] <- sqrt((order_data_sum[ii,"mz"] - PC_record_sum$mz[loc[jj]])^2)
                  }  
                  mz_rt_diff_min <- which.min(mz_rt_diff) 
                  order_data_sum$Designate_PC[ii] <- PC_record_sum$Designate_PC[loc][mz_rt_diff_min] 
                  #updating the chemical shift of the PC  
                  Update_PC <- PC_record_sum$Designate_PC[loc][mz_rt_diff_min]  
                  PC_loc <- which(Update_PC==order_data_sum$Designate_PC)  
                  #H_rt <- order_data_sum[PC_loc,"rt"]
                  H_mz <- order_data_sum[PC_loc,"mz"]
                  #PC_record_sum$rt[loc][mz_rt_diff_min] <- mean(H_rt)
                  #PC_record_sum$rt[loc][mz_rt_diff_min] <- H_rt[length(PC_loc)] 
                  PC_record_sum$mz[loc][mz_rt_diff_min] <- mean(H_mz)	
                  #PC_record_sum$mz[loc][mz_rt_diff_min] <- H_mz[length(PC_loc)]
                }else{  
                  #peak is uniquely designated to a specific PC
                  order_data_sum$Designate_PC[ii] <- PC_record_sum$Designate_PC[loc]
                  #updating the chemical shift of the PC
                  Update_PC <- PC_record_sum$Designate_PC[loc]
                  PC_loc <- which(order_data_sum$Designate_PC == Update_PC)
                  #H_rt <- order_data_sum[PC_loc,"rt"]
                  H_mz <- order_data_sum[PC_loc,"mz"]
                  PC_record_sum$mz[loc] <- mean(H_mz)
                  #PC_record_sum$rt[loc] <- mean(H_rt)
                  #PC_record_sum$rt[loc] <- H_rt[length(PC_loc)] 
                  #PC_record_sum$mz[loc] <- H_mz[length(PC_loc)] 			
                }   
                # If the chosen peak is located outside the tolerance of any existing PC     
              }else{
                n_sum <- length(PC_record_sum$Designate_PC)+1   
                order_data_sum$Designate_PC[ii] = paste("PC",n_sum,sep = "")   
                PC_record_sum <- rbind(PC_record_sum,order_data_sum[ii,c("mz","intensity","Designate_PC")]) 
              }    
            }	
          }
          
          if(length(order_data_sum$Designate_PC) == 1){
            PC_record_sum <- PC_record_sum
            order_data_sum <- order_data_sum
          }
          #对相同的编号按强度加和    
          sum1a <- order_data_sum %>%
            group_by(Designate_PC) %>%
            summarise_at(vars(2), sum)
          
          sum1 <- as.data.frame(sum1a)
          #合并二级碎片mz信息
          for(p in 1:dim(sum1)[1]){
            for(q in 1:dim(PC_record_sum)[1]){
              if (sum1$Designate_PC[p] %in% PC_record_sum$Designate_PC[q]){
                sum1[p,3:5] <- PC_record_sum[q,]
              }
            }
          }
          #写出同一离子的二级碎片加和结果        
          matrix_ms2 <- sum1[,c(3,2)]
          matrix_ms2_all[[nm]] <- arrange(matrix_ms2,mz)
          matrix_ms1_all <- rbind(matrix_ms1_all,matrix_ms1)
          mzdec <- 4; rtdec <- 3;
          rtfmt <- paste("%.", rtdec, "f", sep = "")
          mzfmt <- paste("%.", mzdec, "f", sep = "")
          var1<- paste("nM", sprintf(mzfmt, matrix_ms1_all[,"mz"]), "T", sprintf(rtfmt, matrix_ms1_all[,"rt"]), sep = "")
          rownames(matrix_ms1_all) <- var1
          # 创建一个新的Excel工作簿
          wb <- createWorkbook()
          # 循环遍历列表中的矩阵，并将它们添加到工作簿中
          for (i in seq_along(matrix_ms2_all)) {
            # 添加一个新的工作表，名称为矩阵的名称
            addWorksheet(wb, sheetName = rownames(matrix_ms1_all)[i])
            # 将矩阵写入当前工作表
            writeData(wb, sheet = rownames(matrix_ms1_all)[i], x = matrix_ms2_all[[i]])
          }
          # 保存工作簿到文件
          #saveWorkbook(wb, paste(file.pos_m,".xlsx",sep = ""), save_path, overwrite = TRUE)
          saveWorkbook(wb, file = file.path(save_path, paste0(basename(file.pos_m), ".xlsx")), overwrite = TRUE)
        }
      }
    }
  }

  if(length(order_data$Designate_PC) == 1){
    # 创建一个新的Excel工作簿
    wb <- createWorkbook()
    # 添加一个新的工作表，名称为矩阵的名称
    addWorksheet(wb, sheetName = order_data[,1])
    # 将矩阵写入当前工作表
    writeData(wb, sheet = order_data[,1], x = ms2.info.pos[[1]])
    
    # 保存工作簿到文件
    #saveWorkbook(wb, paste(file.pos_m,".xlsx",sep = ""), save_path, overwrite = TRUE)
    saveWorkbook(wb, file = file.path(save_path, paste0(basename(file.pos_m), ".xlsx")), overwrite = TRUE)
  }
  
  matrix_ms1_all_m <- rbind(matrix_ms1_all_m,matrix_ms1_all) 
  
}



############################hebingjieshu

# 读取所有采集的excel
path <- file.path("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/01_excel_he")
file.pos <- mixedsort(dir(file.path(path), pattern = "\\.xlsx$", full.names = TRUE))
name_all <- NULL
for(l in 1:length(file.pos)){
  # 加载Excel文件
  wb <- file.pos[l]
  name_l <- getSheetNames(wb)
  name_l_file <- data.frame(name_l,wb)
  name_all <- rbind(name_all,name_l_file)
}
dim(name_all)
name_all[which(grepl("mz\\d+(\\.\\d+)?", name_all$name_l)),]$name_l <- gsub("mz", "nM", gsub("rt", "T", (name_all[which(grepl("mz\\d+(\\.\\d+)?", name_all$name_l)),])$name_l))
# 移除前缀'nM'，并以'T'为分隔符分割剩余的字符串
df_processed <- name_all %>%
  mutate(
    # 移除前缀'nM'
    ProcessedData = stringr::str_remove(name_l, "^nM"),
    # 分割处理后的数据
    Part1 = stringr::str_split_fixed(ProcessedData, "T", 2)[, 1],
    Part2 = stringr::str_split_fixed(ProcessedData, "T", 2)[, 2]
  )
# 查看结果
print(df_processed)
# 如果你不再需要原始的SampleData列或ProcessedData列，可以选择性地删除它们
df_final <- df_processed %>%
  select( -ProcessedData)
# 查看最终的数据框
print(df_final)
colnames(df_final) <- c("name_l","wb","mz","rt")
write.csv(df_final, "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/allms2_acqired_1.csv")


###2.1生成MyData_excel
library(readxl)
library(openxlsx)
library(parallel)

input_folder  <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/01_excel_he"  # 替换为你的 Excel 文件所在路径
output_folder <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/02_MyData_excel"  # 替换为你想要保存 CSV 文件的路径

# 创建输出文件夹，如果不存在则创建
if(!dir.exists(output_folder)){
  dir.create(output_folder)
}

# 获取文件夹中的所有 Excel 文件
excel_files <- dir(input_folder, pattern = "\\.xlsx$", full.names = TRUE)

# 定义一个处理单个文件的函数
process_file <- function(file) {
  # 获取当前 Excel 文件中的所有工作表名称
  sheet_names <- excel_sheets(file)
  
  # 遍历每一个工作表
  for(sheet in sheet_names){
    sheet_data <- read_excel(file, sheet = sheet)
    output_file <- file.path(output_folder, paste0(sheet, ".csv"))
    write.csv(sheet_data, output_file, row.names = FALSE)
  }
}

num_cores <- detectCores() - 2  
cl <- makeCluster(num_cores)
clusterEvalQ(cl, {
  library(readxl)
  library(openxlsx)
})

# 导出需要的变量和函数到集群
clusterExport(cl, varlist = c("process_file", "input_folder", "output_folder", "excel_files"))
parLapply(cl, excel_files, process_file)

stopCluster(cl)

# #将差异变量和已经采集的二级数据进行匹配20240819

############################################################################
#20250303
#3333——循环查找precur_aqi 和path 中文件夹名称中第二个_之后，.xlsx之前
#数据的差值是否在正负0.005之内，是的话，将path 中符合要求的文件复制一份到save_path中
st <- Sys.time(); st
library(gtools)  
library(openxlsx)  
library(stringr)  

path <- file.path("D:/DDDDDD/output_tables1")  
file.pos <- mixedsort(dir(path, pattern = "\\.xlsx$|\\.xls$", full.names = TRUE))  

result_dir <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/03_database_excel02"  
if (!dir.exists(result_dir)) {  
  dir.create(result_dir, recursive = TRUE)  
}  

file_path_ms2 <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/02_MyData_excel"  
file.pos_diffaqui <- mixedsort(dir(file.path(file_path_ms2), pattern = "\\.csv$", full.names = TRUE))  

for (n in 1:length(file.pos_diffaqui)) {  
    # n=165
 precur_aqi <- as.numeric(str_extract(basename(file.pos_diffaqui[n]), "\\d+\\.\\d+"))  
  
for (m in 1:length(file.pos)) { 
    # m=1
    file <- file.pos[m]  
    
    # 从文件名中提取第二个 "_" 之后到 ".xlsx" 之前的部分  
    file_name_parts <- str_split(basename(file), "_")[[1]]  
    
    if (length(file_name_parts) >= 3) {  
      second_part_value_str <- gsub("\\.xlsx$", "", file_name_parts[3])  # 去掉 .xlsx  
      second_part_value <- as.numeric(second_part_value_str)  
       
      difference <- abs(precur_aqi - second_part_value)  
      if (difference <= 0.005) {  
       
        file.copy(file, file.path(result_dir, basename(file)), overwrite = TRUE)  
      }  
    }  
  }  
}

et<-Sys.time(); et
et-st
save.image("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/03_database_excel_20250303_7.8h.Rdata")

########上面并行20250303
#3333——循环查找precur_aqi 和path 中文件夹名称中第二个_之后，.xlsx之前
#数据的差值是否在正负0.005之内，是的话，将path 中符合要求的文件复制一份到save_path中

# 有损坏用不了并行

####################20250205非并行----对对对
st <- Sys.time(); st

library(stringr)  
library(openxlsx)  
library(gtools)  

path <- file.path("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/02_MyData_excel")  
path_base <- file.path("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/03_database_excel02")  

file.pos <- mixedsort(dir(path, pattern = "\\.csv$", full.names = TRUE))  
file.pos_base <- mixedsort(dir(path_base, pattern = "\\.xlsx$", full.names = TRUE))  

dir.create("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/04_df_final_database_posthoc1", showWarnings = FALSE)  
dir.create("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/05_df_final_aquired_match_posthoc1", showWarnings = FALSE)  

mzTol <- 0.005   

for (pq in 1:length(file.pos)) {  
  # pq=13
  mz_aqi <- as.numeric(str_extract(file.pos[pq], "\\d+\\.\\d+"))  # 提取质量数  
  matched_files <- list()  
  
  for (pd in 1:length(file.pos_base)) {  
    base_file_name <- basename(file.pos_base[pd])  
    base_file_name_clean <- gsub("\\.xlsx$", "", base_file_name)  # 去掉扩展名  
    mz_base <- as.numeric(tail(str_split(base_file_name_clean, "_")[[1]], n = 1))  
    
    # 检查质量数是否在容差范围内  
    if (mz_aqi >= mz_base - mzTol & mz_aqi <= mz_base + mzTol) {  
      # 复制文件到目标文件夹  
      target_file <- file.path("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/05_df_final_aquired_match_posthoc1", basename(file.pos[pq]))  
      file.copy(file.pos[pq], target_file, overwrite = TRUE)  
      
      # 创建输出文件夹并复制基础文件  
      output_folder <- file.path("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/04_df_final_database_posthoc1", mz_aqi)  
      dir.create(output_folder, showWarnings = FALSE)  
      
      target_file <- file.path(output_folder, basename(file.pos_base[pd]))  
      file.copy(file.pos_base[pd], target_file, overwrite = TRUE)  
    }  
  }  
}

et<-Sys.time(); et
et-st

save.image("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/04_05_database_excel_20250304_1.2h.Rdata")
#@@@@@@@@@@@@@@@@@

##########################################################################################
#差异变量与数据库的二级数据匹配计算相似度
#4444.单向匹配
library(dplyr)
library(stringr)
library(openxlsx)
library(readxl)
library(gtools)  

start_time <- Sys.time()
cosine_similarity <- function(vec1, vec2) {
  dot_product <- sum(vec1 * vec2)
  norm_vec1 <- sqrt(sum(vec1^2))
  norm_vec2 <- sqrt(sum(vec2^2))
  similarity <- dot_product / (norm_vec1 * norm_vec2)
  return(similarity)
}

filepath2 <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/05_df_final_aquired_match_posthoc1"    # 520
mzfiles2 <- mixedsort(list.files(filepath2, recursive = TRUE, full.names = TRUE))
filepath1_base <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/04_df_final_database_posthoc1"      # 基础路径
#dim(filepath1_base)

result_list_all <- NULL

for(m in 1:length(mzfiles2)){
  result_list <- NULL
       # m=798
  data_3_new <- read.csv(mzfiles2[m], 1)
  data_3_new <- arrange(data_3_new, mz)
  # 从文件名中提取m/z值
  mz_values_3 <- str_extract(mzfiles2[m], "\\d+\\.\\d+")
  mz_values_3 <- as.numeric(mz_values_3)
  #去除二级数据中的母离子
  mztol <- 0.005
  if(length(which(abs(data_3_new$mz - mz_values_3) <= mztol)) > 0){
    data_3 <- data_3_new[-which(abs(data_3_new$mz - mz_values_3) <= mztol),]
  }else{
    data_3 <- data_3_new
  }
  if(nrow(data_3) >= 2){file_name2
    data_3$Loss_Neutral <- abs(mz_values_3 - data_3$mz)
    # 计算归一化强度
    data_3$intensity <- sqrt(data_3$intensity)
    data_3$relative_intensity <- (data_3$intensity)/sum(data_3$intensity)
    #根据母离子指定到数据库中的文件夹
    sim <- 0
    file_name2 <- gsub("\\.csv$", "", basename(mzfiles2[m]))
    mz_values_3 <- as.numeric(str_extract(file_name2, "\\d+\\.\\d+"))
    # 在每次循环中动态生成 mzfiles1 列表
    mulizi <- mz_values_3
    filepath1_base <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/04_df_final_database_posthoc1"        # 基础路径
    mzfiles1 <- list.files(paste0(filepath1_base,"/",mulizi), recursive = TRUE, full.names = TRUE)
    if(length(mzfiles1) > 0){
      for (s in 1:length(mzfiles1)) {
        # s=12
        sim <- 0
        sheet2_4 <- read.xlsx(mzfiles1[s], sheet = 2,colNames = F)
        data_4_new <- read.xlsx(mzfiles1[s], sheet = 1)
        data_4_new <- arrange(data_4_new, mz)
        # 从文件名中提取m/z值
        file_name1 <- gsub("\\.xlsx$", "", basename(mzfiles1[s]))
        mz_values_4 <- as.numeric(str_extract(file_name1, "\\d+\\.\\d+"))
        
        data_4_new$mz <- as.numeric(data_4_new$mz)  
        #去除二级数据中的母离子
        if(length(which(abs(data_4_new$mz - mz_values_4) <= mztol)) > 0){
          data_4 <- data_4_new[-which(abs(data_4_new$mz - mz_values_4) <= mztol),]
        }else{
          data_4 <- data_4_new
        }
        
        if(nrow(data_4) >= 2){
          data_4$Loss_Neutral <- abs(mz_values_4 - data_4$mz) 
          #计算归一化强度
          data_4$intensity <- sqrt(data_4$intensity)
          data_4$relative_intensity <- (data_4$intensity)/sum(data_4$intensity)
          
          #提取数据库中sheet2中代谢物信息
          # name_4 <- sheet2_4[grepl("Name:", sheet2_4[, 1]), 1]
          # name_4 <- gsub("Name: ", "", name_4)
          # smiles_4 <- sheet2_4[grepl("InChIKey:", sheet2_4[, 1]), 1]
          # smiles_4 <- gsub("InChIKey: ", "", smiles_4)[1]
          
          name_4 <- sheet2_4[grepl("name:", sheet2_4[, 1], ignore.case = TRUE), 1]  
          name_4 <- gsub("name: ", "", name_4, ignore.case = TRUE)  
          
          # precursor_type <- sheet2_4[grepl("PRECURSORTYPE:", sheet2_4[, 1], ignore.case = TRUE), 1]  
          # precursor_type <- gsub("PRECURSORTYPE: ", "", precursor_type, ignore.case = TRUE)  
          # 查找包含 "PRECURSORTYPE:" 的行，支持多种格式  
          precursor_type <- sheet2_4[grepl("(?i)precursor[_\\s-]*type:", sheet2_4[, 1], perl = TRUE), 1]  
          # 提取真正的前体类型，去除前缀，支持不同的格式  
          precursor_type <- gsub("(?i)precursor[_\\s-]*type:\\s*", "", precursor_type, perl = TRUE)  
          if (length(precursor_type) == 0) {  
            precursor_type <- NA  
          } 
          
          formula_4 <- sheet2_4[grepl("FORMULA:", sheet2_4[, 1], ignore.case = TRUE), 1]  
          formula_4 <- gsub("FORMULA: ", "", formula_4, ignore.case = TRUE)  
          if (length(formula_4) == 0) {  
            formula_4 <- NA  
          }  
          # 提取不区分大小写的 InChIKey:  
          smiles_4 <- sheet2_4[grepl("smiles:", sheet2_4[, 1], ignore.case = TRUE), 1]
          smiles_4 <- gsub("smiles: ", "", smiles_4, ignore.case = TRUE)[1]
          
          # 如果没有找到 InChIKey，则提取 SMILES
          if (is.na(smiles_4) || smiles_4 == "") {
            smiles_4 <- sheet2_4[grepl("inchiKey:", sheet2_4[, 1], ignore.case = TRUE), 1]  
            smiles_4 <- gsub("inchiKey: ", "", smiles_4, ignore.case = TRUE)[1]
            
            # smiles_4 <- sheet2_4[grepl("smiles:", sheet2_4[, 1], ignore.case = TRUE), 1]
            # smiles_4 <- gsub("smiles: ", "", smiles_4, ignore.case = TRUE)[1]
          }
          
          # 如果没有找到 SMILES，再提取 InChI
          if (is.na(smiles_4) || smiles_4 == "") {
            smiles_4 <- sheet2_4[grepl("inchi:", sheet2_4[, 1], ignore.case = TRUE), 1]
            smiles_4 <- gsub("inchi: ", "", smiles_4, ignore.case = TRUE)[1]
          }
          
          # 如果仍然没有找到，设置为 NA
          if (is.na(smiles_4) || smiles_4 == "") {
            smiles_4 <- NA
          }
          
          para1 <- 0
          para2 <- NULL
          if(length(para2)> 0){
            Num.3 <- arrange(data_3[data_3$relative_intensity >= para1, ],desc(relative_intensity))[para2,] 
            Num.4 <- arrange(data_4[data_4$relative_intensity >= para1, ],desc(relative_intensity))[para2,]
            
          }else{
            Num.3 <- arrange(data_3[data_3$relative_intensity >= para1, ],desc(relative_intensity))
            Num.4 <- arrange(data_4[data_4$relative_intensity >= para1, ],desc(relative_intensity))
          }
          
          #碎片相似度计算
          MzRet1<-arrange(Num.3,mz)
          MzRet2<-arrange(Num.4,mz)
          
          #确定自己的目的是用于鉴定还是判断分子网络中离子的相似性
          type <- 1
          
          if(type == 1){
            #匹配相同的碎片离子
            N1 = 1:nrow(MzRet1); N2 = 1:nrow(MzRet2) 
            mztol = 0.005; 
            Ind1_mz <- NULL;Ind2_mz <- NULL;
            spec_ALL <- NULL
            for(i in N1){
              mz = MzRet1[i,"mz"]; 
              for(j in N2){
                if (MzRet2[j, "mz"] >= mz - mztol & MzRet2[j, "mz"] <= mz + mztol) {
             
                Ind1_mz <- c(Ind1_mz,i ); Ind2_mz <- c(Ind2_mz,j )
                  
                }
              }
            }
            MzRet1_mz_sim <- MzRet1 %>%  mutate(mzmax = pmax(mz, Loss_Neutral))
            colname_mz <- c("mz","relative_intensity1","relative_intensity2")
            MzRet12_mz_Mat <- cbind(MzRet1_mz_sim[Ind1_mz,c(5,4)],MzRet2[Ind2_mz,4])
            
            if(dim(MzRet12_mz_Mat)[1] >= 2){
              if(length(Ind1_mz) > 0){
                MzRet1_mz_sol <- MzRet1[-Ind1_mz,]
              }else{
                MzRet1_mz_sol <- MzRet1
              }
              if(length(Ind2_mz) > 0){
                MzRet2_mz_sol <- MzRet2[-Ind2_mz,]
              }else{
                MzRet2_mz_sol <- MzRet2
              }
              
              # MzRet1_mz_sol <- MzRet1_mz_sol %>%  mutate(mzmin = pmin(mz, Loss_Neutral))
              # MzRet2_mz_sol <- MzRet2_mz_sol %>%  mutate(mzmin = pmin(mz, Loss_Neutral))
              
              MzRet1_mz_sol <- MzRet1_mz_sol %>% mutate(mzmin = mz)  
              MzRet2_mz_sol <- MzRet2_mz_sol %>% mutate(mzmin = mz)
              
             #合并所有碎片数据
              MzRet1_mz_zero <- rep(0,dim(MzRet1_mz_sol)[1])
              MzRet2_mz_zero <- rep(0,dim(MzRet2_mz_sol)[1])
              
              MzRet1_mz_sol_add <- cbind(MzRet1_mz_sol[c(5,4)],MzRet1_mz_zero)
              MzRet2_mz_sol_add <- cbind(MzRet2_mz_sol[,5],MzRet2_mz_zero,MzRet2_mz_sol[,4])
              colnames(MzRet12_mz_Mat) <- colname_mz
              colnames(MzRet1_mz_sol_add) <- colname_mz
              colnames(MzRet2_mz_sol_add) <- colname_mz
              MzRet_all <- rbind(MzRet12_mz_Mat,MzRet1_mz_sol_add,MzRet2_mz_sol_add)
              
              data <- MzRet_all
              dim(data)
              
              #mz的权重
              spec1_intensity_wei <- sqrt(data$mz)*data$relative_intensity1
              spec2_intensity_wei <- sqrt(data$mz)*data$relative_intensity2
              data1 <- data.frame(round(data[,1],4),spec1_intensity_wei,spec2_intensity_wei)
              
              # 计算余弦相似度
              sim <- cosine_similarity(spec1_intensity_wei, spec2_intensity_wei)
            }  
          }else{
            #############################################
            #第一种情况#匹配相同的离子和中性丢失数据(1)
            N1 = 1:nrow(MzRet1); N2 = 1:nrow(MzRet2) 
            mztol = 0.005; Loss_NeutralTol = 0.005;
            spec_ALL1 <- NULL;
            #匹配二级碎片及中性丢失相同的三种情况（两者均匹配（1种），二者其一匹配（2种））
            for(i in N1){
              mz = MzRet1[i,"mz"]; Loss_Neutral = MzRet1[i,"Loss_Neutral"];
              N2 = 1:nrow(MzRet2) 
              for(j in N2){
                if (MzRet2[j, "mz"] >= mz - mztol & MzRet2[j, "mz"] <= mz + mztol){
                  if (length(which(MzRet2[,"Loss_Neutral"] >= Loss_Neutral - Loss_NeutralTol & MzRet2[, "Loss_Neutral"] <= Loss_Neutral + Loss_NeutralTol)) > 0){
                    ind_both <- which(MzRet2[,"Loss_Neutral"] >= Loss_Neutral - Loss_NeutralTol & MzRet2[, "Loss_Neutral"] <= Loss_Neutral + Loss_NeutralTol)
                    mz_mat <- c(MzRet2[j,"mz"],MzRet2[ind_both,"Loss_Neutral"])
                    int_mat <- c(MzRet2[j,"relative_intensity"],MzRet2[ind_both,"relative_intensity"])
                    int_ind <- which.min(c(abs(diff(c(MzRet1[i,"relative_intensity"],MzRet2[j,"relative_intensity"]))),abs(diff(c(MzRet1[i,"relative_intensity"],MzRet2[ind_both,"relative_intensity"])))))
                    data_same1 <- data.frame(cbind(mz_mat[int_ind],MzRet1[i,"relative_intensity"],int_mat[int_ind]))
                    if(int_ind == 1){
                      colnames(data_same1) <- c("mz","relative_intensity1","relative_intensity2")
                    }else{
                      colnames(data_same1) <- c("Loss_Neutral","relative_intensity1","relative_intensity2")
                    }
                    
                    spec_ALL1[[i]] <- data_same1
                    #colnames(spec_ALL1) <- c("mz","relative_intensity1","relative_intensity2")
                  }   
                }
              }  
            } 
            
            spec_ALL1 <- Filter(Negate(is.null), spec_ALL1)
            
            if(length(spec_ALL1) > 0){
              Loss_Neutral_same  <- do.call(rbind,Filter(function(df) names(df)[1] == "Loss_Neutral", spec_ALL1))
              dim(Loss_Neutral_same)
              mz_same <- do.call(rbind,Filter(function(df) names(df)[1] == "mz", spec_ALL1))
              dim(mz_same)
              
              #合并两者都的情况Loss_Neutral_same、mz_same成为mz_Neutral_both
              Loss_Neutral_same_both <- Loss_Neutral_same
              mz_same_both <- mz_same
              
              if(length(dim(Loss_Neutral_same_both)[1]) > 0){
                colnames(Loss_Neutral_same_both) <- c("mz","relative_intensity1","relative_intensity2")
              }
              if(length(dim(mz_same_both)[1]) > 0){
                colnames(mz_same_both) <- c("mz","relative_intensity1","relative_intensity2")
              }
              mz_Neutral_both <- rbind(Loss_Neutral_same_both,mz_same_both)
              
              #去掉两者都的情况，获得MzRet1_diff、MzRet2_diff
              #给定区间范围用以去除两者都的数据
              #参照谱图
              
              Loss_NeutralTol = 0.005
              index_MzRet1_Neu <- NULL
              if(length(dim(Loss_Neutral_same)[1]) > 0){
                for(m in 1:dim(Loss_Neutral_same)[1]){
                  for(n in 1:dim(MzRet1)[1]){
                    index <- which(Loss_Neutral_same$Loss_Neutral[m] >= MzRet1$Loss_Neutral[n] - Loss_NeutralTol & Loss_Neutral_same$Loss_Neutral[m] <= MzRet1$Loss_Neutral[n] + Loss_NeutralTol)
                    if(length(index) > 0){
                      index_MzRet1_Neu <- c(index_MzRet1_Neu,n)
                    }
                  }
                }
                length(index_MzRet1_Neu)
              }
              
              mzTol = 0.005
              index_MzRet1_mz <- NULL
              if(length(dim(mz_same)[1]) > 0){
                for(m1 in 1:dim(mz_same)[1]){
                  for(n1 in 1:dim(MzRet1)[1]){
                    index1 <- which(mz_same$mz[m1] >= MzRet1$mz[n1] - mzTol & mz_same$mz[m1] <= MzRet1$mz[n1] + mzTol)
                    if(length(index1) > 0){
                      index_MzRet1_mz <- c(index_MzRet1_mz,n1)
                    }
                  }
                }
                length(index_MzRet1_mz)
              }
              
              MzRet1_diff <- MzRet1[-c(index_MzRet1_Neu,index_MzRet1_mz),]
              dim(MzRet1_diff)
              dim(MzRet1)
              
              #数据库谱图
              Loss_NeutralTol = 0.005
              index_MzRet2_Neu_base <- NULL
              if(length(dim(Loss_Neutral_same)[1]) > 0){
                for(m_base in 1:dim(Loss_Neutral_same)[1]){
                  for(n_base in 1:dim(MzRet2)[1]){
                    index_base <- which(Loss_Neutral_same$Loss_Neutral[m_base] >= MzRet2$Loss_Neutral[n_base] - Loss_NeutralTol & Loss_Neutral_same$Loss_Neutral[m_base] <= MzRet2$Loss_Neutral[n_base] + Loss_NeutralTol)
                    if(length(index_base) > 0){
                      index_MzRet2_Neu_base <- c(index_MzRet2_Neu_base,n_base)
                    }
                  }
                }
              }
              
              mzTol = 0.005
              index_MzRet2_mz <- NULL
              if(length(dim(mz_same)[1]) > 0){
                for(m1_base in 1:dim(mz_same)[1]){
                  for(n1_base in 1:dim(MzRet2)[1]){
                    index1_base <- which(mz_same$mz[m1_base] >= MzRet2$mz[n1_base] - mzTol & mz_same$mz[m1_base] <= MzRet2$mz[n1_base] + mzTol)
                    if(length(index1_base) > 0){
                      index_MzRet2_mz <- c(index_MzRet2_mz,n1_base)
                    }
                  }
                }
              }
              
              MzRet2_diff <- MzRet2[-c(index_MzRet2_Neu_base,index_MzRet2_mz),]
              dim(MzRet2_diff)
              dim(MzRet2)
            }else{
              MzRet1_diff <- MzRet1
              MzRet2_diff <- MzRet2
              mz_Neutral_both <- NULL
            }
            
            #第二种情况#碎片和中性丢失其一匹配spec_ALL2、spec_ALL3(2)
            spec_ALL2 <- NULL;
            spec_ALL3 <- NULL;
            N11 = 1:nrow(MzRet1_diff); N22 = 1:nrow(MzRet2_diff) 
            for(p in N11){
              mzp = MzRet1_diff[p,"mz"]; Loss_Neutralp = MzRet1_diff[p,"Loss_Neutral"];
              N22 = 1:nrow(MzRet2_diff) 
              for(q in N22){
                if(MzRet2_diff[q, "mz"] >= mzp - mztol & MzRet2_diff[q, "mz"] <= mzp + mztol){
                  data_same2 <- cbind(MzRet1_diff[p,"mz"],MzRet1_diff[p,"relative_intensity"],MzRet2_diff[q,"relative_intensity"])
                  spec_ALL2 <- rbind(spec_ALL2,data_same2)
                  colnames(spec_ALL2) <- c("mz","relative_intensity1","relative_intensity2")
                }
                
                if(MzRet2_diff[q,"Loss_Neutral"] >= Loss_Neutralp - Loss_NeutralTol & MzRet2_diff[q, "Loss_Neutral"] <= Loss_Neutralp + Loss_NeutralTol){
                  data_same3 <- cbind(MzRet1_diff[p,"Loss_Neutral"],MzRet1_diff[p,"relative_intensity"],MzRet2_diff[q,"relative_intensity"])
                  spec_ALL3 <- rbind(spec_ALL3,data_same3)
                  colnames(spec_ALL3) <- c("Loss_Neutral","relative_intensity1","relative_intensity2")
                }
              }   
            } 
            
            #其一匹配spec_ALL2，spec_ALL3定为spec_ALL2_mz、spec_ALL3_neu
            spec_ALL2_mz <- spec_ALL2
            spec_ALL3_neu <- spec_ALL3
            if(length(dim(spec_ALL2_mz)[1]) > 0){
              colnames(spec_ALL2_mz) <- c("mz","relative_intensity1","relative_intensity2")
            } 
            if(length(dim(spec_ALL3_neu)[1]) > 0){
              colnames(spec_ALL3_neu) <- c("mz","relative_intensity1","relative_intensity2")
            } 
            
            #去掉其一匹配数据，获取剩余未匹配数据
            index_MzRet1_Neu <- NULL
            if(length(dim(spec_ALL3)[1]) > 0){
              Loss_NeutralTol = 0.005
              for(m in 1:dim(spec_ALL3)[1]){
                for(n in 1:dim(MzRet1_diff)[1]){
                  index <- which(spec_ALL3[,"Loss_Neutral"][m] >= MzRet1_diff$Loss_Neutral[n] - Loss_NeutralTol & spec_ALL3[,"Loss_Neutral"][m] <= MzRet1_diff$Loss_Neutral[n] + Loss_NeutralTol)
                  if(length(index) > 0){
                    index_MzRet1_Neu <- c(index_MzRet1_Neu,n)
                  }
                }
              }
              length(index_MzRet1_Neu)
            }
            
            index_MzRet1_mz <- NULL
            if(length(dim(spec_ALL2)[1]) > 0){
              mzTol = 0.005
              for(m1 in 1:dim(spec_ALL2)[1]){
                for(n1 in 1:dim(MzRet1_diff)[1]){
                  index1 <- which(spec_ALL2[,"mz"][m1] >= MzRet1_diff$mz[n1] - mzTol & spec_ALL2[,"mz"][m1] <= MzRet1_diff$mz[n1] + mzTol)
                  if(length(index1) > 0){
                    index_MzRet1_mz <- c(index_MzRet1_mz,n1)
                  }
                }
              }
              length(index_MzRet1_mz)
            }
            
            if (length(index_MzRet1_Neu) == 0 && length(index_MzRet1_mz) == 0) {
              MzRet1_diff1 <- MzRet1_diff
            }else{
              MzRet1_diff1 <- MzRet1_diff[-c(index_MzRet1_Neu,index_MzRet1_mz),]
            }  
            dim(MzRet1_diff)
            dim(MzRet1_diff1)
            
            #数据库谱图
            index_MzRet2_Neu_base <- NULL
            if(length(dim(spec_ALL3)[1]) > 0){
              Loss_NeutralTol = 0.005
              for(m_base in 1:dim(spec_ALL3)[1]){
                for(n_base in 1:dim(MzRet2_diff)[1]){
                  index_base <- which(spec_ALL3[,"Loss_Neutral"][m_base] >= MzRet2_diff$Loss_Neutral[n_base] - Loss_NeutralTol & spec_ALL3[,"Loss_Neutral"][m_base] <= MzRet2_diff$Loss_Neutral[n_base] + Loss_NeutralTol)
                  if(length(index_base) > 0){
                    index_MzRet2_Neu_base <- c(index_MzRet2_Neu_base,n_base)
                  }
                }
              }
              length(index_MzRet2_Neu_base)
            }
            
            index_MzRet2_mz1 <- NULL
            if(length(dim(spec_ALL2)[1]) > 0){
              mzTol = 0.005
              for(m1_base1 in 1:dim(spec_ALL2)[1]){
                for(n1_base1 in 1:dim(MzRet2_diff)[1]){
                  index1_base1 <- which(spec_ALL2[,"mz"][m1_base1] >= MzRet2_diff$mz[n1_base1] - mzTol & spec_ALL2[,"mz"][m1_base1] <= MzRet2_diff$mz[n1_base1] + mzTol)
                  if(length(index1_base1) > 0){
                    index_MzRet2_mz1 <- c(index_MzRet2_mz1,n1_base1)
                  }
                }
              }
              length(index_MzRet2_mz1)
            }
            
            if (length(index_MzRet1_Neu) == 0 && length(index_MzRet1_mz) == 0) {
              MzRet2_diff1 <- MzRet2_diff
            }else{
              MzRet2_diff1 <- MzRet2_diff[-c(index_MzRet2_Neu_base,index_MzRet2_mz1),]
            }
            
            dim(MzRet2_diff)
            dim(MzRet2_diff1)
            
            MzRet1_diff_zero <- rep(0,dim(MzRet1_diff1)[1])  
            MzRet2_diff_zero <- rep(0,dim(MzRet2_diff1)[1]) 
            
            # MzRet1_diff1 <- MzRet1_diff1 %>% mutate(min_value = pmin(mz, Loss_Neutral))
            # MzRet2_diff1 <- MzRet2_diff1 %>% mutate(min_value = pmin(mz, Loss_Neutral))
            MzRet1_diff1 <- MzRet1_diff1 %>% mutate(min_value = mz)
            MzRet2_diff1 <- MzRet2_diff1 %>% mutate(min_value = mz)
            
            dim(MzRet1_diff1)
            dim(MzRet2_diff1)
            
            MzRet1_diff_add <- cbind(data.frame(MzRet1_diff1[,5]),data.frame(MzRet1_diff1[,4]),data.frame(MzRet1_diff_zero)) 
            MzRet2_diff_add <- cbind(data.frame(MzRet2_diff1[,5]),data.frame(MzRet2_diff_zero),data.frame(MzRet2_diff1[,4])) 
            colnames(MzRet1_diff_add) <- c("mz","relative_intensity1","relative_intensity2")
            colnames(MzRet2_diff_add) <- c("mz","relative_intensity1","relative_intensity2")
            MzRet1_diff_both <- rbind(MzRet1_diff_add,MzRet2_diff_add)
            
            #合并所有匹配的数据
            common_three <- rbind(mz_Neutral_both,spec_ALL2_mz,spec_ALL3_neu)
            if(dim(common_three)[1] >= 2){
              data <- rbind(common_three,MzRet1_diff_both) 
              #mz的权重
              spec1_intensity_wei <- sqrt(data$mz)*data$relative_intensity1
              spec2_intensity_wei <- sqrt(data$mz)*data$relative_intensity2
              data1 <- data.frame(round(data[,1],4),spec1_intensity_wei,spec2_intensity_wei)
              
              # 计算余弦相似度
              sim <- cosine_similarity(spec1_intensity_wei, spec2_intensity_wei)
            }
          }
          result <- data.frame(File2 = file_name2, File1 = file_name1, Sim_Value = sim, Name1 = name_4, MW = formula_4,SMILES1 = smiles_4,Adduct_type = precursor_type,stringsAsFactors = FALSE)
          result_list <- rbind(result_list,result)
        }
      }
    }
  }
  result_list_all <- rbind(result_list_all, result_list)
}

end_time <- Sys.time() 
execution_time <- end_time - start_time
print(paste("Execution time:", execution_time))  

save.image("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/sim_pick_3.4h_0304.Rdata")
load("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/sim_pick_3.4h_0304.Rdata")

#对相似度结果进行预筛选
sim_tol <- 0.5
result_list_all <- result_list_all[result_list_all[,"Sim_Value"] >= sim_tol,]
#View(result_list_all)

write.csv(result_list_all,"F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/06_result_list_all1_617.11_m_806.csv")
###########
#result_list_all <- read.csv("F:/00_SHUITI_SHUJU/01_ZQ_SPL_dataProcess_end/result_2222/neg_chun_spl/06_result_list_all1.csv")  
result_list_all <- na.omit(result_list_all)
result_list_all <- result_list_all[!is.na(result_list_all$SMILES1) & result_list_all$SMILES1 != "", ]
#@@@@@@@@@找到被过滤的
# 创建一个逻辑向量，找出需要过滤掉的行  
to_filter <- is.na(result_list_all$SMILES1) | result_list_all$SMILES1 == ""  
filtered_out_data <- result_list_all[to_filter, ]  
result_list_all <- result_list_all[!to_filter, ]  
#write.csv(filtered_out_data, "F:/00_SHUITI_SHUJU/01_ZQ_SPL_dataProcess_end/result_2222/neg_chun_spl/filtered_out_data.csv", row.names = FALSE)
#@@@@@@@@@

# 按照实验数据的母离子进行分组
result_list_all$File2_value <- str_extract(result_list_all$File2, "\\d+\\.\\d+")
result_list_all$File2_value <- as.numeric(result_list_all$File2_value)
# 排序数据
sorted_data <- result_list_all %>%
  arrange(File2_value)
# 创建一个分组标签
group_labels <- sorted_data %>%
  mutate(Group_ID = cumsum(c(TRUE, diff(File2_value) >= 0.005))) %>%
  mutate(Group_Label = paste0("PC", Group_ID)) %>%
  select(File2, Group_Label) %>%
  distinct()
# 将分组标签合并到原始数据框中
filtered_data_with_labels <- result_list_all %>%
  left_join(group_labels, by = "File2")

filtered_data_with_labels <- arrange(filtered_data_with_labels,Group_Label)
dim(filtered_data_with_labels)
library(gtools)
pc_lab <- mixedsort(unique(filtered_data_with_labels$Group_Label))
sim_tol <- 0.2
pc_sim <- NULL
for(i in 1:length(pc_lab)){
  pc_lab_i <- pc_lab[i]
  pc_group_ind <- which(filtered_data_with_labels$Group_Label == pc_lab_i)
  
  if (length(pc_group_ind) > 1){
    pc_group <- filtered_data_with_labels[pc_group_ind,]
    pc_group$Name1 <- toupper(pc_group$Name1)
    #ind_grep <- grep("UNKNOWN",pc_group$Name1)
    ind_grep <- grep("UNKNOWN", pc_group$Name1, ignore.case = TRUE)
    pc_group_sim <- pc_group[-ind_grep,]
    if(length(ind_grep) > 0){
      pc_group_sim <- pc_group_sim
    }else{
      pc_group_sim <- pc_group
    }
    
    pc_group_max <- which.max(pc_group_sim$Sim_Value)
    ind <- NULL
    for (s in 1:length(pc_group_sim$Sim_Value)){
      if(pc_group_sim$Sim_Value[s] >= pc_group_sim$Sim_Value[pc_group_max] - sim_tol){
        ind <- c(ind,s)
      }
    }
    pc_group_sim_sel <- pc_group_sim[ind,]
    split_string <- strsplit(pc_group_sim_sel$Name1, ";")
    first_elements <- unlist(lapply(split_string, function(x) x[1]))
    pc_group_sim_sel$Name1 <- first_elements
    sim_name <- unique(pc_group_sim_sel$Name1)
    
    pc_group_sel_fil_all <- NULL
    for(f in 1:length(sim_name)){
      # f=22
      pc_group_sel <- arrange(pc_group_sim_sel[pc_group_sim_sel$Name1 %in% sim_name[f],],desc(Sim_Value))
      if(dim(pc_group_sel)[1]<3){
        pc_group_sel_fil <- pc_group_sel
      }else{
        pc_group_sel_fil <- pc_group_sel[1:3,]
      }
      pc_group_sel_fil_all <- rbind(pc_group_sel_fil_all,pc_group_sel_fil)
    }
  }else{
    pc_group_sel_fil_all <- filtered_data_with_labels[pc_group_ind,]
  }
  pc_sim <- rbind(pc_sim,pc_group_sel_fil_all)
}

output_file <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/07_diffvarial_match_database_angilent_tof_20250218.xlsx"
write.xlsx (pc_sim, output_file)



#output_file SMILES1列中相同的并且Group_Label为一组的内容，保留Sim_Value中最大值的一行内容
library(dplyr)  
library(readxl) 
library(openxlsx)
 
# output_file <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/07_diffvarial_match_database_angilent_tof_20250206.xlsx"  
data <- read_excel(output_file)  

result <- data %>%  
  group_by(Group_Label, SMILES1) %>%  
  # 按Sim_Value降序排列  
  arrange(desc(Sim_Value)) %>%  
  # 保留每组中Sim_Value最大的行  
  slice(1) %>%  
  ungroup()   

# 输出结果到 Excel 文件  
output_result_file <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/filtered_result.xlsx"  
write.xlsx(result, output_result_file, rowNames = FALSE)  


############
#鉴定结果，去除重复的，
####0218汇总整理结果中的excel表格，将adduct从结果18中筛选到pos_huizong_results_1209中，正或者负的水/醇，4个sheet一起提出脚本注释的结果
library(dplyr)  
library(readxl)  
library(openxlsx)  
library(stringr)  

Origin_date1 <- read.csv("F:/00_SHUITI_SHUJU/01_ZQ_shuiChun_20241107_MS1/result_20241110/pos_shuiti/18_Alladduct_end_pn.csv")
Origin_date2 <- read.csv("F:/00_SHUITI_SHUJU/01_ZQ_shuiChun_20241107_MS1/result_20241110/pos_shuiti/18_Alladduct_end_pn.csv")  
excel_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/filtered_result.xlsx"  
sheet_names <- getSheetNames(excel_path)  

results_list <- list()  
mz_tol<- 0.01  
rt_tol <- 6

for (i in 1:length(sheet_names)) { 
  #i=1
  sheet <- sheet_names[i]   
  output_file <- read.xlsx(excel_path, sheet = sheet)  
  
  # 提取File2中的T后面数值，并创建rt列  
  output_file <- output_file %>%  
    mutate(rt = str_extract(`File2`, "(?<=T)\\d+\\.\\d+")) %>%  
    mutate(File2_value = as.numeric(str_extract(`File2`, "\\d+\\.\\d+"))) # 提取File2_value  
  
  # 将rt转换为数字类型  
  output_file <- output_file %>%   
    mutate(rt = as.numeric(rt))  
  
  # 根据i的值选择相应的Origin_date进行比较  
  if (i %% 2 == 0) {                        #i %% 2 是一个模（求余）运算符，返回 i 除以 2 的余数
    # i为2或4时，使用Origin_date1  
    Origin_date <- Origin_date1  
  } else {  
    # i为1或3时，使用Origin_date2  
    Origin_date <- Origin_date2  
  }  
  
  # 进行比较，并将满足条件的Origin_date中的adduct提取到output_file中  
  output_file <- output_file %>%  
    rowwise() %>%  
    mutate(adduct = list(Origin_date$adduct[  
      abs(File2_value - Origin_date$mz) <= mz_tol & abs(rt - Origin_date$rt) <= rt_tol  
    ])) %>%  
    ungroup()  
  
  # 如果需要，将adduct列展开为字符  
  output_file <- output_file %>%   
    mutate(adduct = sapply(adduct, function(x) ifelse(length(x) > 0, paste(x, collapse = ", "), NA)))  
  
  # 去除adduct列中的数字部分  
  output_file <- output_file %>%  
    mutate(adduct = str_replace_all(adduct, "\\s?\\d+\\.\\d+", "")) # 去除数字部分及任何空格  
  # 将处理后的数据添加到结果列表中  
  results_list[[sheet]] <- output_file  
}  


output_excel_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/08_add_adduct_result.xlsx"  
write.xlsx(results_list, output_excel_path)  

# #####提取匹配到的数据库中子文件夹中excel中的sheet2中的第一列中PRECURSORTYPE:后面的内容
# #01 将匹配到结果的数据库中的筛选出来---根据excel_path中第二列内容，
# #从target_folder 中删选出子文件夹名字和excel_path中第二列内容相同的，复制到output_path中
# 
library(dplyr)
library(openxlsx)
excel_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/filtered_result.xlsx"
target_folder <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/03_database_excel02"
output_path <-   "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/10_df_final_database_posthoc_0304"
# 读取 Excel 文件，并提取第二列内容
excel_data <- read.xlsx(excel_path)  # 读取 Excel 文件
name_list <- unique(excel_data[[2]])  # 提取第二列的唯一值
name_list_df <- data.frame(File1 = name_list)
# 获取目标文件夹中的所有 Excel 文件
excel_files <- list.files(target_folder, pattern = "\\.xlsx$", full.names = TRUE)
# 提取文件名（不包括路径和扩展名）
excel_file_names <- tools::file_path_sans_ext(basename(excel_files))
excel_file_names_df <- data.frame(file_name = excel_file_names)
common_data <- inner_join(name_list_df, excel_file_names_df, by = c("File1" = "file_name"))
# unique_to_name_list <- anti_join(name_list_df, common_data, by = c("File1" = "File1"))
matching_files <- excel_files[excel_file_names %in% common_data$File1]

# 创建输出文件夹（如不存在）
if (!dir.exists(output_path)) {
  dir.create(output_path, recursive = TRUE)
}

# 复制匹配的 Excel 文件到输出路径
if (length(matching_files) > 0) {
  for (file in matching_files) {
    # 生成输出路径
    file_name <- basename(file)
    target_file_path <- file.path(output_path, file_name)

    # 复制文件
    file.copy(file, target_file_path)
    message(paste("复制文件:", file_name))
  }
} else {
  message("没有找到匹配的文件!")
}


# 02 提取数据库中的注释信息到最后的结果列
library(openxlsx)  # 确保你已加载openxlsx库  
library(dplyr)     # 确保你已加载dplyr库  

source_folder <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/10_df_final_database_posthoc_0304"  
target_folder <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/10_df_final_database_posthoc_0304"  
output_excel_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/08_add_adduct_result.xlsx"  

# 定义要处理的工作表名称  
sheet_names <- c("pos_shui_result", "filtered_chun_results1_sig")  

# 初始化列表保存处理结果  
combined_results <- list()  
remaining_data_list <- list()  

# 遍历指定的工作表名称，逐一处理数据  
for (j in 1:length(sheet_names)) {  
  j=1
  sheet_name <- sheet_names[j]  
  output_data <- read.xlsx(output_excel_path, sheet = sheet_name)  
  
  if (ncol(output_data) >= 2) {  
    target_names <- trimws(output_data[[2]])  
  } else {  
    stop("输出文件中没有足够的列！")  
  }  
  
  # 初始化用于存储PRECURSORTYPE的数据的向量  
  adduct_data <- vector("list", length(target_names))  
  formula_4_list <- vector("list", length(target_names))  # 存储每个目标名称的formula_4  
  processed_data <- output_data  
  
  # 遍历目标名称，并找到匹配的Excel文件  
  for (i in 1:length(target_names)) {  
    name <- target_names[i]  
    
    matching_excel_files <- list.files(target_folder, pattern = paste0("^", name, ".*\\.xlsx$"), full.names = TRUE)  
    
    if (length(matching_excel_files) > 0) {  
      for (file in matching_excel_files) {  
        sheet_data <- read.xlsx(file, sheet = 2)  
        
        # 提取formula_4  
        formula_4 <- sheet_data[grepl("FORMULA:", sheet_data[, 1], ignore.case = TRUE), 1]  
        formula_4 <- gsub("FORMULA: ", "", formula_4, ignore.case = TRUE)  
        formula_4_list[[i]] <- ifelse(length(formula_4) > 0, formula_4, NA)  # 确保有值或返回NA  
        
        # 查找包含 "PRECURSORTYPE:" 或 "Precursor_type:" 的行  
        precursor_row <- grep("(?i)precursor[_\\s-]*type:", sheet_data[[1]], perl = TRUE)  
        
        if (length(precursor_row) > 0) {  
          precursor_value <- trimws(sub(".*?(?i)precursor[_\\s-]*type:\\s*", "", sheet_data[[1]][precursor_row]))  
          adduct_data[[i]] <- precursor_value  
        } else {  
          adduct_data[[i]] <- NA  
        }  
      }  
    } else {  
      adduct_data[[i]] <- NA  
      formula_4_list[[i]] <- NA  
    }  
  }  
  
  # 将格式化后的adduct_data添加到processed_data的最后一列  
  processed_data$adduct_data <- unlist(adduct_data)  
  
  # 确保 formula_4 的长度与 processed_data 的行数一致  
  length_check <- length(formula_4_list) == nrow(processed_data)  
  if (!length_check) {  
    formula_4_list <- c(unlist(formula_4_list), rep(NA, nrow(processed_data) - length(formula_4_list)))  
  }  
  
  # 将formula_4列表转为向量并添加到processed_data  
  processed_data$formula_4 <- unlist(formula_4_list)  
  
  MW <- processed_data$formula_4  
  processed_data$formula_4 <- NULL  
  processed_data <- cbind(processed_data[, 1:8], MW, processed_data[, 9:ncol(processed_data)])  
  
  # 继续后续处理  
  processed_data$adduct_data <- gsub("^\\Q[M+H]\\E$", "[M+H]+", processed_data$adduct_data)  
  processed_data$adduct_data <- gsub("^M\\+H$", "[M+H]+", processed_data$adduct_data)  # 匹配 M+H  
  processed_data$adduct_data <- gsub("^M\\+Na$", "[M+Na]+", processed_data$adduct_data)  # 匹配 M+Na
  processed_data$adduct_data <- gsub("^M\\+K$", "[M+K]+", processed_data$adduct_data)  # 匹配 M+Na
  processed_data$adduct_data <- gsub("^M\\+NH4$", "[M+NH4]+", processed_data$adduct_data)  # 匹配 M+Na
  # 获取最后一列和倒数第二列的列名  
  last_col_name <- colnames(processed_data)[ncol(processed_data)]  
  penultimate_col_name <- colnames(processed_data)[ncol(processed_data) - 1]  
  
  # 筛选最后一列和倒数第二列相同的行  
  matching_rows <- processed_data %>%  
    filter(!!sym(last_col_name) == !!sym(penultimate_col_name))  
  
  # 筛选最后一列不为空且倒数第二列为空的行  
  not_empty <- processed_data %>%  
    filter(!is.na(!!sym(last_col_name)) & is.na(!!sym(penultimate_col_name)))  
  
  # 汇总这两个数据框  
  combined_results[[sheet_name]] <- bind_rows(matching_rows, not_empty)  
  
  combined_dataframe <- bind_rows(combined_results, .id = "source")  
  remaining_data <- processed_data %>%  
    anti_join(combined_dataframe, by = colnames(processed_data))  
  remaining_data_list[[sheet_name]] <- remaining_data  
}  


combined_output_path <- sub("\\.xlsx$", "pos_shui_add_adduct_results_0304.xlsx", output_excel_path)  
write.xlsx(combined_results, combined_output_path, asTable = TRUE)  
write.xlsx(processed_data, "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/12_pos_shui_processed_data_0304.xlsx")  




#20250308提出SMILES号，用于后续化合物NPclassfire分组
library(ggplot2)  
library(readxl)  
library(dplyr)  
library(openxlsx)  
library(gtools) 

output_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/10_df_final_database_posthoc_0304"  
mzfiles2 <- mixedsort(list.files(output_path, recursive = TRUE, full.names = TRUE))  
filtered_results <- read.xlsx("F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/11_pos_shui_add_adduct_results_0304.xlsx", sheet = 1)  
filtered_results$SMILES <- NA  

for (i in 1:nrow(filtered_results)) { 
  # i=1
  target_filename <- filtered_results[i, 2]  
  matched_file_index <- which(basename(mzfiles2) == paste0(target_filename, ".xlsx")) 
  if (length(matched_file_index) > 0) {  
    data <- read.xlsx(mzfiles2[matched_file_index], sheet = 2)  
    smiles_row_index <- which(grepl("SMILES:", data[,1], ignore.case = TRUE)) # 假设 SMILES 在第一列  
    if (length(smiles_row_index) > 0) {  
      smiles_string <- data[smiles_row_index, 1] # 假设 SMILES 在第一列  
      smiles <- sub("(?i)^SMILES:\\s*", "", smiles_string)
      filtered_results$SMILES[i] <- smiles  
    }  
  }  
}  

write.xlsx(filtered_results, "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/11_pos_shui_add_adduct_results_0304_with_smiles.xlsx")  











#20250220自己鉴定的结果只保留最大的sim值
library(ggplot2)
library(readxl)
file_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/11_pos_shui_add_adduct_results_0304.xlsx"
data <- read_excel(file_path)
result <- data %>%group_by(Group_Label) %>% slice(which.max(Sim_Value))   # 按照 PC 列进行分组  
write.xlsx(result, "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/13_pos_shui_processed_data_with_maxSim.xlsx")




















#20250220 统计MS-DIAL结果中有MS2的总数
# library(ggplot2)  
# library(readxl)  
# library(dplyr)  
# 
# file_path <- "F:/00_SHUITI_SHUJU/01_ZQ_SPL_dataProcess_end/result_1111/HuiZong_result_20241209/01_pos_huizong_result_shui_msdial_1210.xlsx"  
# sheet_names <- excel_sheets(file_path)  
# 
# # 初始化一个空列表以存储每个工作表的结果  
# results <- list()  
# 
# # 循环遍历每个工作表  
# for (sheet in 1:length(sheet_names)) {  
#   # 读取当前工作表的数据  
#   data <- read_excel(file_path, sheet = sheet)  
#   
#   # 过滤出 Name 列中不包含 "no MS2" 的行  
#   filtered_data <- data %>%  
#     filter(!grepl("no MS2", Name, ignore.case = TRUE))  # ignore.case = TRUE 确保不区分大小写  
#   
#   # 计算符合条件的行数  
#   count <- nrow(filtered_data)  
#   
#   # 将结果存储在列表中  
#   results[[sheet]] <- count  
# }  
# 
# # 计算所有工作表符合条件的行数总和  
# total_count <- sum(unlist(results))  
# print(total_count)
# 
# 
# # 
# library(ggplot2)  
# library(readxl)  
# library(dplyr)  
# 
# file_path <- "F:/00_SHUITI_SHUJU/01_ZQ_SPL_dataProcess_end/result_1111/HuiZong_result_20241209/01_pos_huizong_result_shui_msdial_1210.xlsx"  
# sheet_names <- excel_sheets(file_path)  
# 
# # 初始化一个空列表以存储每个工作表的结果  
# results <- list()  
# 
# # 循环遍历每个工作表  
# for (sheet in 1:length(sheet_names)) {  
#   # 读取当前工作表的数据  
#   data <- read_excel(file_path, sheet = sheet)  
#   
#   # 过滤出 Name 列中不包含 "no MS2" 和 "Unknown" 的行  
#   filtered_data <- data %>%  
#     filter(!grepl("no MS2", Name, ignore.case = TRUE) &   
#              !grepl("Unknown", Name, ignore.case = TRUE))  # 同时排除 "Unknown"   
#   
#   # 计算符合条件的行数  
#   count <- nrow(filtered_data)  
#   
#   # 将结果存储在列表中  
#   results[[sheet]] <- count  
# }  
# 
# # 计算所有工作表符合条件的行数总和  
# total_count <- sum(unlist(results))  
# print(total_count)


# 20250220
# 在原有筛选条件（不包含 "no MS2" 的行）基础上，同时排除 "Unknown" 的行
library(ggplot2)  
library(readxl)  
library(dplyr)  

file_path <- "F:/00_SHUITI_SHUJU/01_ZQ_SPL_dataProcess_end/result_1111/HuiZong_result_20241209/01_pos_huizong_result_shui_msdial_1210.xlsx"  
sheet_names <- excel_sheets(file_path)  

# 初始化列表以存储符合不同条件的结果  
results_no_ms2 <- list()  
results_no_ms2_and_unknown <- list()  

# 初始化总行数  
total_rows <- 0  

# 从第 2 个工作表开始循环到最后一个工作表  
for (sheet in 2:length(sheet_names)) {  
  # 读取当前工作表的数据  
  data <- read_excel(file_path, sheet = sheet)  
  
  # 计算当前工作表的总行数并累加到 total_rows  
  total_rows <- total_rows + nrow(data)  
  
  # 过滤出 Name 列中不包含 "no MS2" 的行  
  filtered_data_no_ms2 <- data %>%  
    filter(!grepl("no MS2", Name, ignore.case = TRUE))  
  count_no_ms2 <- nrow(filtered_data_no_ms2)  
  results_no_ms2[[sheet]] <- count_no_ms2  
  
  # 过滤出 Name 列中不包含 "no MS2" 和 "Unknown" 的行  
  filtered_data_no_ms2_and_unknown <- data %>%  
    filter(!grepl("no MS2", Name, ignore.case = TRUE) &   
             !grepl("Unknown", Name, ignore.case = TRUE))  
  count_no_ms2_and_unknown <- nrow(filtered_data_no_ms2_and_unknown)  
  results_no_ms2_and_unknown[[sheet]] <- count_no_ms2_and_unknown  
}  

# 计算所有工作表符合条件的行数总和  
total_count_no_ms2 <- sum(unlist(results_no_ms2))  
total_count_no_ms2_and_unknown <- sum(unlist(results_no_ms2_and_unknown))  

# 打印结果  
cat("Total count (not 'no MS2'):", total_count_no_ms2, "\n")  
cat("Total count (not 'no MS2' and not 'Unknown'):", total_count_no_ms2_and_unknown, "\n")  
cat("Total rows from sheet 2 to last sheet:", total_rows, "\n")



#20250219--概率分布图
# library(ggplot2) 
# library(readxl)  
# file_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/11_pos_shui_add_adduct_results_0218.xlsx"  
# data <- read_excel(file_path)  
# sim_values <- data$Sim_Value 
# 
# # 计算在指定范围内的比例  
# proportion <- sum(sim_values >= 0.85 & sim_values <= 0.99) / length(sim_values)  
# cat("在0.8到0.9范围内的比例：", proportion * 100, "%\n")  
# 
# # 创建概率分布图  
# ggplot(data.frame(value = sim_values), aes(x = value)) +  
#   geom_density(fill = "lightblue", alpha = 0.7) +  
#   geom_vline(xintercept = c(0.85, 0.99), linetype = "dashed", color = "red") +  
#   labs(title = "Probability Density Function",  
#        x = "Value",  
#        y = "Density") +  
#   theme_minimal()  



# 创建概率分布图  
# ggplot(data, aes(x = Sim_Value)) +  
#   geom_density(fill = "blue", alpha = 0.5) +  
#   labs(title = "Sim Value Probability Distribution", x = "Sim Value", y = "Density") +  
#   theme_minimal()


# 加载必要的库  
library(ggplot2)   
library(readxl)  
file_path <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/11_pos_shui_add_adduct_results_0218.xlsx"  
data <- read_excel(file_path)  
sim_values <- data$Sim_Value   

# 计算在指定范围内的比例  
proportion <- sum(sim_values >= 0.85 & sim_values <= 0.99) / length(sim_values)  
cat("在0.85到0.99范围内的比例：", proportion * 100, "%\n")  

# 创建概率分布图，包括直方图和密度图  
ggplot(data.frame(value = sim_values), aes(x = value)) +  
  geom_histogram(aes(y = after_stat(density)), bins = 30, fill = "lightblue", color = "black", alpha = 0.7) +  # 直方图  
  geom_density(fill = "red", alpha = 0.5) +  # 密度图  
  geom_vline(xintercept = c(0.85, 0.99), linetype = "dashed", color = "red") +  # 虚线  
  labs(title = "Probability Density Function",  
       x = "Value",  
       y = "Density") +  
  theme_minimal()

# MS-DIAL 水提（+）

library(ggplot2)  
data <- c(1.89, 1.87, 1.85, 1.85, 1.83, 1.83, 1.82, 1.82, 1.82, 1.82,  
          1.82, 1.81, 1.80, 1.79, 1.79, 1.79, 1.79, 1.79, 1.78, 1.78,  
          1.76, 1.75, 1.75, 1.75, 1.74, 1.74, 1.74, 1.72, 1.72, 1.71,  
          1.70, 1.70, 1.69, 1.69, 1.68, 1.67, 1.67, 1.67, 1.67, 1.66,  
          1.65, 1.64, 1.64, 1.64, 1.62, 1.61, 1.60, 1.59, 1.59, 1.54,  
          1.54, 1.51, 1.50, 1.47, 1.46, 1.45)  

# 创建数据框用于绘图  
data_df <- data.frame(value = data)  

# 绘制密度图和直方图  
ggplot(data_df, aes(x = value)) +  
  geom_histogram(aes(y = after_stat(density)), bins = 15, fill = "lightblue", color = "black", alpha = 0.7) +  
  geom_density(alpha = 0.5, fill = "red") +  
  labs(title = "Distribution of Values",  
       x = "Value",  
       y = "Density") +  
  theme_minimal()






#####批量画实验数据与数据库二级数据匹配的数据20250325

####@@@@@@@@并行画图_deng_20250308
library(stringr)
library(parallel)
library(openxlsx)
library(dplyr)
library(ggplot2)
library(ggtext)
library(readxl)

output_folder <- "D:/DYL_20250210/compare_20250324/pos_20250325/compare_figure_20250404"
if (!dir.exists(output_folder)) dir.create(output_folder, recursive = TRUE)

# 读取结果数据
filtered_results <- read_excel("D:/DYL_20250210/Addtional_information_20250327/standard_pos.xlsx", sheet = 2)

# 获取文件名和元数据
file_names1 <- filtered_results$File1
file_names2 <- filtered_results$File2
first_column_data <- filtered_results$Name
seconed_column_data <- filtered_results$'SMILES'

# 设置文件路径
filepath1 <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/05_df_final_aquired_match_posthoc1"
filepath2 <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/03_database_excel02"
mzfiles1 <- list.files(filepath1, pattern = "\\.csv$", recursive = TRUE, full.names = TRUE)
mzfiles2 <- list.files(filepath2, pattern = "\\.xlsx$", recursive = TRUE, full.names = TRUE)



# 刻度取整函数
round_to_5 <- function(x) {
  5 * round(x / 5)
}
# 并行处理每一对文件
num_cores <- 2
cl <- makeCluster(num_cores)  
clusterExport(cl, list("mzfiles1", "filtered_results","mzfiles2", "first_column_data", "seconed_column_data","round_to_5", "output_folder"))  # 导出所需对象到集群
clusterEvalQ(cl, library(openxlsx))
clusterEvalQ(cl, library(dplyr))
clusterEvalQ(cl, library(ggplot2))
clusterEvalQ(cl, library(ggtext))

parLapply(cl, seq_along(seconed_column_data), function(i) {
  data1 <- read.csv(mzfiles1[which(basename(mzfiles1) %in% paste0(filtered_results$File2[i], ".csv"))])
  data2 <- read.xlsx(mzfiles2[which(basename(mzfiles2) %in% paste0(filtered_results$File1[i], ".xlsx"))[1]], sheet = 1)
  first_column <- first_column_data[i]
  
  # 数据类型转换
  df1 <- data1
  df2 <- data2
  df1$mz <- as.numeric(df1$mz)
  df2$mz <- as.numeric(df2$mz)
  df1$intensity <- as.numeric(df1$intensity)
  df2$intensity <- as.numeric(df2$intensity)
  df1$intensity <- df1$intensity / max(df1$intensity)
  df2$intensity <- -df2$intensity / max(df2$intensity)
  df <- rbind(df1, df2)
  
  # 获取文件名和相似度信息
  name1 <- filtered_results$File2[i]
  name2 <- filtered_results$File1[i]
  sim <- round(filtered_results$Sim_Value[i], 4)
  
  # 设置标题并换行
  name <- paste0(name1, " vs. ", name2, "\n", first_column, "\n", "Similarity = ", sim)
  
  # 绘图
  pic <- ggplot(df, aes(x = mz, y = intensity)) +
    geom_col(aes(fill = ifelse(intensity > 0, "blue", "red")), width = 0.5) +
    labs(
      subtitle = name, 
      x = "mz", y = "intensity"
    ) +
    theme_minimal() +
    theme(
      panel.border = element_rect(color = "black", size = 1.5, fill = NA),
      panel.grid.major = element_line(size = 0.2),
      panel.grid.minor = element_line(size = 0.05),
      axis.text = element_text(size = 8),
      axis.ticks.length = unit(-0.05, "cm"),
      legend.position = "none"
    ) +
    scale_x_continuous(
      limits = c(round_to_5(min(df$mz) - 5), round_to_5(max(df$mz) + 5)),
      breaks = round(seq(round_to_5(min(df$mz) - 5), round_to_5(max(df$mz) + 5), length.out = 10)),
      minor_breaks = NULL
    ) +
    scale_y_continuous(
      limits = c(-1, 1),
      breaks = seq(-1, 1, 0.2)
    ) +
    scale_fill_manual(values = c("red", "blue"), guide = "none")
  
  # 保存图像
  output_path <- paste0(output_folder, "/Fig", i, "_", name1, "vs.", name2, ".pdf")
  ggsave(filename = output_path, plot = pic, dpi = 900, width = 10, height = 6, units = "in", bg = "white")
})

stopCluster(cl)  # 关闭集群



###danhe_20250325
library(stringr)
library(openxlsx)
library(dplyr)
library(ggplot2)
library(ggtext)

# 设置输出目录
output_folder <- "D:/DYL_20250210/compare_20250324/pos_20250325/compare_figure_20250404"
if (!dir.exists(output_folder)) dir.create(output_folder, recursive = TRUE)

filtered_results <- read_excel("D:/DYL_20250210/Addtional_information_20250327/standard_pos.xlsx", sheet = 2)
# 获取文件名和元数据
file_names1 <- filtered_results$File1
file_names2 <- filtered_results$File2
first_column_data <- filtered_results$Name
seconed_column_data <- filtered_results$'SMILES'

# 设置文件路径
filepath1 <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/05_df_final_aquired_match_posthoc1"
filepath2 <- "F:/00_SHUITI_SHUJU/04_ZQ_identificationResult_0303/Result_0303/pos_shui_spl/03_database_excel02"
mzfiles1 <- list.files(filepath1, pattern = "\\.csv$", recursive = TRUE, full.names = TRUE)
mzfiles2 <- list.files(filepath2, pattern = "\\.xlsx$", recursive = TRUE, full.names = TRUE)

# 刻度取整函数
round_to_5 <- function(x) { 5 * round(x / 5) }

# 逐个处理每对文件
for (i in seq_len(nrow(filtered_results))) {
  tryCatch({
    # 精确匹配文件名
    csv_file <- grep(paste0("^", filtered_results$File2[i], "\\.csv$"), 
                     basename(mzfiles1), value = TRUE, ignore.case = TRUE)
    xlsx_file <- grep(paste0("^", filtered_results$File1[i], "\\.xlsx$"), 
                      basename(mzfiles2), value = TRUE, ignore.case = TRUE)
    
    if (length(csv_file) == 0 || length(xlsx_file) == 0) {
      message(paste("跳过第", i, "行：未找到匹配文件"))
      next
    }
    
    # 读取数据
    data1 <- read.csv(file.path(filepath1, csv_file[1]))
    data2 <- read.xlsx(file.path(filepath2, xlsx_file[1]), sheet = 1)
    
    # 数据预处理
    df1 <- data1 %>% mutate(mz = as.numeric(mz), 
                            intensity = as.numeric(intensity)/max(as.numeric(intensity)))
    df2 <- data2 %>% mutate(mz = as.numeric(mz), 
                            intensity = -as.numeric(intensity)/max(as.numeric(intensity)))
    df <- bind_rows(df1, df2)
    
    # 准备图表标题
    title <- paste0(filtered_results$File2[i], " vs. ", filtered_results$File1[i], "\n",
                    filtered_results$Name[i], "\n", 
                    "相似度 = ", round(filtered_results$Sim_Value[i], 4))
    
    # 绘制图表
    p <- ggplot(df, aes(x = mz, y = intensity)) +
      geom_col(aes(fill = ifelse(intensity > 0, "实测数据", "数据库数据")), width = 0.5) +
      labs(title = title, x = "质荷比(m/z)", y = "相对强度") +
      scale_fill_manual(values = c("数据库数据" = "red", "实测数据" = "blue")) +
      theme_minimal() +
      theme(legend.position = "none",
            plot.title = element_text(hjust = 0.5))
    
    # 保存图表
    output_file <- paste0(output_folder, "/Fig", i, "_",
                          filtered_results$File2[i], "_vs_",
                          filtered_results$File1[i], ".png")
    ggsave(output_file, plot = p, dpi = 900, width = 10, height = 6)

    message(paste("成功处理:", output_file))
    
    # 保存为PDF格式
    # output_file <- paste0(output_folder, "/Fig", i, "_", 
    #                       filtered_results$File2[i], "_vs_", 
    #                       filtered_results$File1[i], ".pdf")
    # 
    # ggsave(output_file, 
    #        plot = p, 
    #        device = "pdf", 
    #        width = 8.27,   # A4纸宽度(英寸)
    #        height = 5.83,  # A4纸高度/2(英寸)
    #        units = "in",
    #        dpi = 300)
    # 
    # message(paste("成功处理:", output_file))
    
    
  }, error = function(e) {
    message(paste("处理第", i, "行时出错:", e$message))
  })
}