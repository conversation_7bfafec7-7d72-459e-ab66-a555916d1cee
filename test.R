
# 需要的包
library(readr)
library(openxlsx)
library(stringr)


input_path <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/input_test"

output_base_path <- "C:/AAA-HUYU/Laboratory/R_project/DYL/20250731_Msp_search/output"

# 检查路径是否有效
if (input_path == "") {
  cat("未输入输入路径，使用当前文件夹\n")
  input_path <- "."
}

if (output_base_path == "") {
  cat("未输入输出路径，使用默认路径：./output\n")
  output_base_path <- "./output"
}

# 1. 获取指定文件夹中的所有.msp文件
msp_files <- list.files(path = input_path, pattern = "\\.msp$", full.names = TRUE)

if (length(msp_files) == 0) {
  cat(sprintf("在路径 %s 中没有找到.msp文件！\n", input_path))
  quit()
}

cat(sprintf("在 %s 中找到 %d 个.msp文件，开始批量处理...\n", input_path, length(msp_files)))
cat(sprintf("输出路径: %s\n", output_base_path))

# 批量处理每个.msp文件
for (file_idx in seq_along(msp_files)) {
  msp_file <- msp_files[file_idx]
  cat(sprintf("正在处理: %s\n", basename(msp_file)))

  # 读取msp文件
  lines <- readLines(msp_file, encoding = "UTF-8")

  # 2. 解析内容
  get_blocks <- function(lines) {
    blocks <- list()
    block <- c()
    for (line in lines) {
      if (str_trim(line) == "" && length(block) > 0) {
        blocks[[length(blocks) + 1]] <- block
        block <- c()
      } else {
        block <- c(block, line)
      }
    }
    if (length(block) > 0) blocks[[length(blocks) + 1]] <- block
    return(blocks)
  }

  blocks <- get_blocks(lines)

  # 新建输出文件夹
  output_dir <- file.path(output_base_path, tools::file_path_sans_ext(basename(msp_file)))
  if (!dir.exists(output_dir)) dir.create(output_dir, recursive = TRUE)

  # 创建POS和NEG子文件夹
  pos_dir <- file.path(output_dir, "POS")
  neg_dir <- file.path(output_dir, "NEG")
  if (!dir.exists(pos_dir)) dir.create(pos_dir)
  if (!dir.exists(neg_dir)) dir.create(neg_dir)

  # 3. 处理每个block
  for (i in seq_along(blocks)) {
    block <- blocks[[i]]
    # 找到NAME和Num Peaks的位置
    name_idx <- grep("^NAME", block, ignore.case = TRUE)
    num_peaks_idx <- grep("^Num Peaks", block, ignore.case = TRUE)
    if (length(name_idx) == 0 || length(num_peaks_idx) == 0) next

    # 提取元信息和峰信息
    meta_info <- block[name_idx:(num_peaks_idx-1)]

    # 分离Num Peaks行和峰数据
    num_peaks_line <- block[num_peaks_idx]  # Num Peaks行
    peaks_data_start <- num_peaks_idx + 1   # 峰数据从Num Peaks下一行开始

    # 提取峰数据（从Num Peaks下一行到下两个空行之间）
    peaks_raw <- c()
    empty_count <- 0
    for (j in peaks_data_start:length(block)) {
      if (str_trim(block[j]) == "") {
        empty_count <- empty_count + 1
        if (empty_count == 2) break
      } else {
        peaks_raw <- c(peaks_raw, block[j])
        empty_count <- 0
      }
    }

    # 处理峰数据：按空格分割为mz和intens两列
    if (length(peaks_raw) > 0) {
      # 分割每行数据
      peaks_split <- strsplit(peaks_raw, "\\s+")
      # 提取mz和intens
      mz_values <- sapply(peaks_split, function(x) if(length(x) >= 1) x[1] else "")
      intens_values <- sapply(peaks_split, function(x) if(length(x) >= 2) x[2] else "")
      # 创建数据框
      peaks_df <- data.frame(
        mz = mz_values,
        intens = intens_values,
        stringsAsFactors = FALSE
      )
    } else {
      # 如果没有峰数据，创建空的数据框
      peaks_df <- data.frame(
        mz = character(0),
        intens = character(0),
        stringsAsFactors = FALSE
      )
    }

    # 4. 生成文件名
    msp_filename <- basename(msp_file)
    msp_prefix <- str_extract(msp_filename, "^[0-9]+")
    if (is.na(msp_prefix)) {
      msp_prefix <- tools::file_path_sans_ext(msp_filename)
    }
    name_num <- i
    # 提取PRECURSORMZ:后面的数字
    precursor_mz_line <- grep("^PRECURSORMZ:", block, ignore.case = TRUE)
    if (length(precursor_mz_line) > 0) {
      precursor_mz_value <- str_extract(block[precursor_mz_line[1]], "[0-9]+\\.[0-9]+")
      if (is.na(precursor_mz_value)) {
        precursor_mz_value <- "0"
      }
    } else {
      precursor_mz_value <- "0"
    }

    # 提取IONMODE:后面的值
    ionmode_line <- grep("^IONMODE:", block, ignore.case = TRUE)
    if (length(ionmode_line) > 0) {
      ionmode_value <- str_trim(sub("^IONMODE:", "", block[ionmode_line[1]]))
      if (tolower(ionmode_value) == "positive") {
        target_dir <- pos_dir
      } else if (tolower(ionmode_value) == "negative") {
        target_dir <- neg_dir
      } else {
        target_dir <- output_dir  # 默认放在主文件夹
      }
    } else {
      target_dir <- output_dir  # 默认放在主文件夹
    }

    out_file <- file.path(target_dir, sprintf("%s_%d_%s.xlsx", msp_prefix, name_num, precursor_mz_value))

    # 5. 写入Excel
    wb <- createWorkbook()
    addWorksheet(wb, "sheet1")
    addWorksheet(wb, "sheet2")

    # Sheet1: 峰数据，包含mz和intens两列
    writeData(wb, "sheet1", peaks_df, colNames = TRUE)

    # Sheet2: Num Peaks行
    writeData(wb, "sheet2", data.frame(num_peaks_line), colNames = FALSE)

    saveWorkbook(wb, out_file, overwrite = TRUE)
  }

  cat(sprintf("完成处理: %s\n", basename(msp_file)))
}

cat("所有.msp文件处理完成！\n")
